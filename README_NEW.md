# 智慧图片生成 MCP 工具 - 智能模型选择版

基于魔搭平台API，为网页开发提供智能图片生成服务的MCP（Model Context Protocol）工具。

## ✨ 核心特性

- 🤖 **智能模型选择**: 根据用户需求自动选择最适合的AI模型
- 🎨 **多模型支持**: 内置3个精选专业模型
- 🧠 **内容分析**: 智能分析网页内容并生成适配图片
- 📏 **尺寸推断**: 根据用途自动推断最适合的图片尺寸
- 🔧 **零配置**: 只需3个环境变量即可使用
- 📊 **评分系统**: 基于多维度评估自动选择最优模型

## 🤖 内置模型

### 1. 苏-FLUX小红书极致真实V2
- **适用**: 真实风格、人物肖像、生活场景、小红书内容
- **特点**: 极致真实感、社交媒体风格

### 2. 麦橘超然 MajicFlus
- **适用**: 高质量人像、亚洲女性、角色设计
- **特点**: 专注人像质量、细节丰富

### 3. FLUX.1-Kontext-dev-lora-ArtAug
- **适用**: 艺术创作、美学增强、创意设计
- **特点**: 自动美学增强、色彩优化

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
npm run build
```

### 3. 配置MCP（简化版）
```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node",
      "args": ["D:\\cursor_tool\\mymcp\\build\\index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-api-key",
        "OUTPUT_DIR": "D:\\cursor_tool\\mymcp\\static",
        "MAX_IMAGE_SIZE": "2048"
      }
    }
  }
}
```

## 🛠 可用工具

### 1. generate_image
基础图片生成功能
```json
{
  "prompt": "一个优雅的亚洲女性肖像",
  "width": 768,
  "height": 1024,
  "imageType": "portrait"
}
```

### 2. analyze_and_generate（推荐）
智能分析生成
```json
{
  "webpageContent": "这是一个美食博客，需要温馨的生活场景图片",
  "imageType": "hero",
  "context": "小红书风格，真实感强"
}
```

### 3. smart_size_inference
智能尺寸推断
```json
{
  "imageType": "avatar",
  "usageContext": "用户头像"
}
```

### 4. list_available_models
查看所有可用模型
```json
{}
```

## 🧠 智能选择机制

系统会根据以下因素自动选择最适合的模型：

1. **图片类型匹配** (30分) - 根据imageType参数
2. **风格要求适配** (25分) - 分析style参数
3. **提示词内容分析** (25-35分) - 智能分析提示词内容
4. **质量要求评估** (20分) - 根据quality参数

## 📊 使用示例

### 人像生成
```
输入: "生成一个优雅的亚洲女性肖像"
系统: 自动选择 MajicFlus 模型 ✅
```

### 真实场景
```
输入: "小红书风格的咖啡店场景"
系统: 自动选择 小红书极致真实V2 模型 ✅
```

### 艺术创作
```
输入: "创作一幅抽象艺术作品"
系统: 自动选择 ArtAug 模型 ✅
```

## 📁 重要文件

- `mcp-config-intelligent.json` - 简化MCP配置
- `INTELLIGENT_MODEL_GUIDE.md` - 详细使用指南
- `test_api_format.js` - API格式测试脚本

## 🔧 环境变量

只需要3个环境变量：

- `MODELSCOPE_API_KEY`: 魔搭平台API密钥（必需）
- `OUTPUT_DIR`: 图片输出目录
- `MAX_IMAGE_SIZE`: 最大图片尺寸限制

## 🎯 优势

- **零配置**: 无需手动选择模型
- **智能匹配**: 根据需求自动选择最优模型
- **简化使用**: 用户只需关注创作内容
- **可扩展**: 易于添加新模型和优化算法

## 📖 详细文档

查看 `INTELLIGENT_MODEL_GUIDE.md` 获取完整使用指南。

## 🔄 更新日志

### v2.0.0 - 智能模型选择版
- ✅ 修正API调用格式，使用官方标准格式
- ✅ 实现智能模型选择系统
- ✅ 简化配置，只需3个环境变量
- ✅ 内置3个精选专业模型
- ✅ 基于评分系统的自动模型选择

## 许可证

MIT License
