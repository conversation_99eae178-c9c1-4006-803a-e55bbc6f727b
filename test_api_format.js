#!/usr/bin/env node

/**
 * 测试魔搭平台API格式
 * 验证API调用是否正确
 */

import axios from 'axios';
import fs from 'fs';

// 测试API调用
async function testModelScopeAPI() {
  const apiKey = 'ms-a6e8141a-97c2-4f11-91ad-373e20a342ba';
  const url = 'https://api-inference.modelscope.cn/v1/images/generations';
  
  const testCases = [
    {
      name: '小红书模型测试',
      model: 'yiwanji/FLUX_xiao_hong_shu_ji_zhi_zhen_shi_V2',
      prompt: '一个美丽的亚洲女性，温柔的笑容'
    },
    {
      name: 'MajicFlus模型测试',
      model: 'MAILAND/majicflus_v1',
      prompt: '优雅的女性肖像'
    },
    {
      name: 'ArtAug模型测试',
      model: 'DiffSynth-Studio/FLUX.1-Kontext-dev-lora-ArtAug',
      prompt: 'Enhance the aesthetic quality of this image. 抽象艺术作品'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🧪 ${testCase.name}`);
    console.log(`模型: ${testCase.model}`);
    console.log(`提示词: ${testCase.prompt}`);
    
    try {
      const payload = {
        model: testCase.model,
        prompt: testCase.prompt
      };
      
      console.log(`📤 发送请求...`);
      console.log(`URL: ${url}`);
      console.log(`Payload:`, JSON.stringify(payload, null, 2));
      
      const response = await axios.post(url, JSON.stringify(payload), {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000
      });
      
      console.log(`✅ 响应状态: ${response.status}`);
      console.log(`📄 响应数据:`, JSON.stringify(response.data, null, 2));
      
      if (response.data && response.data.images && response.data.images[0]) {
        console.log(`🖼️ 图片URL: ${response.data.images[0].url}`);
      } else {
        console.log(`❌ 未找到图片URL`);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败:`, error.response?.data || error.message);
    }
    
    console.log(`${'='.repeat(50)}`);
  }
}

// 主函数
async function main() {
  console.log('🚀 魔搭平台API格式测试\n');
  
  try {
    await testModelScopeAPI();
  } catch (error) {
    console.error('测试失败:', error);
  }
  
  console.log('\n✅ 测试完成');
  console.log('如果API调用成功，说明MCP服务器的API格式是正确的');
}

main().catch(console.error);
